'''
Problem 5: Sum Root to Leaf Numbers

Understand: 
1.  What happens if the height is 10, would it be 10000000000?
2.  What if the tree is empty?

Plan:
1.  Create a helper function
2.  Update the current sum by appending the node's value
3.  Return the sum if the node is a leaf
4.  Call it recursively if the node has children
5.  Return the sum of the left and right subtrees

Implement:
'''

class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

def sum_numbers(root):
    def dfs(node, current_sum):
        if not node:
            return 0

        current_sum = current_sum * 10 + node.val

        if not node.left and not node.right:
            return current_sum

        return dfs(node.left, current_sum) + dfs(node.right, current_sum)

    return dfs(root, 0)

root1 = TreeNode(1, TreeNode(2), TreeNode(3))
print(sum_numbers(root1))  # Output: 25

root2 = TreeNode(4, TreeNode(9, TreeNode(5), TreeNode(1)), TreeNode(0))
print(sum_numbers(root2))  # Output: 1026

'''
Problem 4: Group Anagrams

Understand:
1.  What if there are no anagrams?
2.  What happens if there is an empty string

Plan:
1.  Create a dictionary to hold the anagrams
2.  Iterate through the list of strings
3.  Sort the string
4.  Add the string to the dictionary
5.  Return the values of the dictionary

Implement:
'''
def group_anagrams(strs):
    anagram_dict = {}
    for string in strs:
        sorted_string = ''.join(sorted(string))
        if sorted_string in anagram_dict:
            anagram_dict[sorted_string].append(string)
        else:
            anagram_dict[sorted_string] = [string]
    return list(anagram_dict.values())

strs = ["eat","tea","tan","ate","nat","bat"]
print(group_anagrams(strs))  # Output: [["bat"],["nat","tan"],["ate","eat","tea"]]

strs2 = [""]
print(group_anagrams(strs2))  # Output: [[""]]

strs3 = ["a"]
print(group_anagrams(strs3))  # Output: [["a"]]

'''
Problem 3: Shuffle Merge

Understand:
1.  What if the lists are different lengths?
2.  What if one list is empty?

Plan:
1.  Create a dummy node to hold the head of the list
2.  Create a pointer to the dummy node
3.  Iterate through the lists
4.  Add the nodes to the new list
5.  Return the next node of the dummy node

Implement:
'''
class ListNode:
    def __init__(self, val=0, next=None):
        self.val = val
        self.next = next

def shuffle_merge(head_a, head_b):
    dummy = ListNode()
    current = dummy

    while head_a and head_b:
        current.next = head_a
        head_a = head_a.next
        current = current.next

        current.next = head_b
        head_b = head_b.next
        current = current.next

    if head_a:
        current.next = head_a
    if head_b:
        current.next = head_b

    return dummy.next

def print_list(node):

    if not node.next:
        return f"{node.val}"
    return f"{node.val} -> {print_list(node.next)}"

head_a = ListNode(1, ListNode(2, ListNode(3)))
head_b = ListNode(4, ListNode(5, ListNode(6)))
print(print_list(shuffle_merge(head_a, head_b)))  # Output: 1 -> 4 -> 2 -> 5 -> 3 -> 6

head_c = ListNode(1, ListNode(2, ListNode(3)))
head_d = ListNode(4)
print(print_list(shuffle_merge(head_c, head_d)))  # Output: 1 -> 4 -> 2 -> 3 
