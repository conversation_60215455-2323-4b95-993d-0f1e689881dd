'''
Problem 3: Minimum Difference in BST

Understand: 
1.  Can the tree have values that are the same?
2.  What if the tree is empty?

Plan:
1.  Create an empty list
2.  Use an in-order traversal to fill the list with the values of the nodes
3.  Iterate through the list and find the minimum difference between consecutive elements

Implement:'''
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

def min_diff_in_bst(root):
    def in_order_traversal(node, values):
        if node:
            in_order_traversal(node.left, values)
            values.append(node.val)
            in_order_traversal(node.right, values)

    values = []
    in_order_traversal(root, values)

    min_diff = float('inf')
    for i in range(1, len(values)):
        diff = values[i] - values[i - 1]
        if diff < min_diff:
            min_diff = diff

    return min_diff

# Example Usage:
root = TreeNode(4, TreeNode(2, TreeNode(1), TreeNode(3)), TreeNode(6))
print(min_diff_in_bst(root))  # Output: 1

# Example Usage 2:
root2 = TreeNode(1, TreeNode(0), TreeNode(48, TreeNode(12), TreeNode(49)))
print(min_diff_in_bst(root2))  # Output: 1

'''
Problem 4: Increasing Order Search Tree
Understand:
1.  What should the output tree look like?
2.  Can the input tree be empty?

Plan:
1.  Initialize an empty list to hold the values of the nodes
2.  Use an in-order traversal to fill the list with the values of the nodes
3.  Create a new tree where each node only has a right child, using the values from the list
4.  Return the new tree

Implement:
'''
def increasing_bst(root):
    values = []
    def in_order_traversal(node):
        if node:
            in_order_traversal(node.left)
            values.append(node.val)
            in_order_traversal(node.right)

    in_order_traversal(root)
    new_root = TreeNode(0)
    current = new_root
    for val in values:
        current.right = TreeNode(val)
        current = current.right

    return new_root.right

def print_tree(node):
    if not node:
        return "None"
    return f"{node.val} -> {print_tree(node.right)}"
# Example Usage:
root = TreeNode(5, TreeNode(1), TreeNode(7))
new_root = increasing_bst(root)
print(print_tree(new_root))  # Output: 1 -> 5 -> 7 -> None

# Example Usage 2:
root2 = TreeNode(5, TreeNode(3, TreeNode(2, TreeNode(1)), TreeNode(4)), 
                 TreeNode(6, None, TreeNode(8, TreeNode(7), TreeNode(9))))
new_root2 = increasing_bst(root2)
print(print_tree(new_root2))  # Output: 1 -> 2 -> 3 -> 4 -> 5 -> 6 -> 7 -> 8 -> 9 -> None

'''
Problem 5: Equal Tree Split
Understand:
1.  What if there are an odd number of nodes?
2.  Can the tree be empty?

Plan:
1.  Create function to calculate the number of node
2.  Create a function to see if you can split the tree
3.  Use a recursive function to check if the tree can be split into two equal trees
4.  If the total sum of the tree is odd, return False
5.  Check if the tree can be split and return the result

Implement:
'''
def count_nodes(root):
    if not root:
        return 0
    return 1 + count_nodes(root.left) + count_nodes(root.right)

def can_split_helper(root, total_nodes):
    if not root:
        return 0
    
    left_count = can_split_helper(root.left, total_nodes)
    right_count = can_split_helper(root.right, total_nodes)
    

    if left_count == total_nodes // 2 or right_count == total_nodes // 2:
        can_split_helper.found = True
    
    return 1 + left_count + right_count

def can_split(root):
    total_nodes = count_nodes(root)
    if total_nodes % 2 != 0:
        return False
    
    can_split_helper.found = False
    can_split_helper(root, total_nodes)
    return can_split_helper.found