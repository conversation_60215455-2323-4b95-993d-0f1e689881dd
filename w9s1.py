'''
Problem 3: Minimum Difference in BST

Understand: 
1.  Can the tree have values that are the same?
2.  What if the tree is empty?

Plan:
1.  Create an empty list
2.  Use an in-order traversal to fill the list with the values of the nodes
3.  Iterate through the list and find the minimum difference between consecutive elements

Implement:'''
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

def min_diff_in_bst(root):
    def in_order_traversal(node, values):
        if node:
            in_order_traversal(node.left, values)
            values.append(node.val)
            in_order_traversal(node.right, values)

    values = []
    in_order_traversal(root, values)

    min_diff = float('inf')
    for i in range(1, len(values)):
        diff = values[i] - values[i - 1]
        if diff < min_diff:
            min_diff = diff

    return min_diff

# Example Usage:
root = TreeNode(4, TreeNode(2, TreeNode(1), TreeNode(3)), TreeNode(6))
print(min_diff_in_bst(root))  # Output: 1

# Example Usage 2:
root2 = TreeNode(1, TreeNode(0), TreeNode(48, TreeNode(12), TreeNode(49)))
print(min_diff_in_bst(root2))  # Output: 1

'''
Problem 4: Increasing Order Search Tree
Understand:
1.  What should the output tree look like?
2.  Can the input tree be empty?

Plan:
1.  Initialize an empty list to hold the values of the nodes
2.  Use an in-order traversal to fill the list with the values of the nodes
3.  Create a new tree where each node only has a right child, using the values from the list
4.  Return the new tree

Implement:
'''
def increasing_bst(root):
    values = []
    def in_order_traversal(node):
        if node:
            in_order_traversal(node.left)
            values.append(node.val)
            in_order_traversal(node.right)

    in_order_traversal(root)
    new_root = TreeNode(0)
    current = new_root
    for val in values:
        current.right = TreeNode(val)
        current = current.right

    return new_root.right

def print_tree(node):
    if not node:
        return "None"
    return f"{node.val} -> {print_tree(node.right)}"
# Example Usage:
root = TreeNode(5, TreeNode(1), TreeNode(7))
new_root = increasing_bst(root)
print(print_tree(new_root))  # Output: 1 -> 5 -> 7 -> None

# Example Usage 2:
root2 = TreeNode(5, TreeNode(3, TreeNode(2, TreeNode(1)), TreeNode(4)), 
                 TreeNode(6, None, TreeNode(8, TreeNode(7), TreeNode(9))))
new_root2 = increasing_bst(root2)
print(print_tree(new_root2))  # Output: 1 -> 2 -> 3 -> 4 -> 5 -> 6 -> 7 -> 8 -> 9 -> None

'''
Problem 5: Equal Tree Split
Understand:
1.  What if there are an odd number of nodes?
2.  Can the tree be empty?

Plan:
1.  Create function to calculate the number of node
2.  Create a function to see if you can split the tree
3.  Use a recursive function to check if the tree can be split into two equal trees
4.  If the total sum of the tree is odd, return False
5.  Check if the tree can be split and return the result

Implement:
'''
# Approach 1: Using a class to avoid global state
class TreeSplitter:
    def __init__(self):
        self.can_split_found = False

    def count_nodes(self, root):
        if not root:
            return 0
        return 1 + self.count_nodes(root.left) + self.count_nodes(root.right)

    def check_split(self, root, target_count):
        if not root:
            return 0

        left_subtree_count = self.check_split(root.left, target_count)
        right_subtree_count = self.check_split(root.right, target_count)

        current_subtree_count = 1 + left_subtree_count + right_subtree_count

        # Check if removing this subtree would create two equal parts
        if current_subtree_count == target_count:
            self.can_split_found = True

        return current_subtree_count

    def can_split(self, root):
        if not root:
            return False

        total_nodes = self.count_nodes(root)
        if total_nodes % 2 != 0:
            return False

        target_count = total_nodes // 2
        self.can_split_found = False
        self.check_split(root, target_count)
        return self.can_split_found

# Approach 2: Using nonlocal variable (more Pythonic)
def can_split_v2(root):
    def count_nodes(node):
        if not node:
            return 0
        return 1 + count_nodes(node.left) + count_nodes(node.right)

    if not root:
        return False

    total_nodes = count_nodes(root)
    if total_nodes % 2 != 0:
        return False

    target_count = total_nodes // 2
    found_split = False

    def dfs(node):
        nonlocal found_split
        if not node or found_split:
            return 0

        left_count = dfs(node.left)
        right_count = dfs(node.right)
        subtree_count = 1 + left_count + right_count

        # If this subtree has exactly half the nodes, we can split here
        if subtree_count == target_count:
            found_split = True

        return subtree_count

    dfs(root)
    return found_split

# Approach 3: Single-pass solution returning tuple
def can_split_v3(root):
    def count_and_check(node, total_nodes):
        """Returns (subtree_count, can_split_found)"""
        if not node:
            return 0, False

        left_count, left_found = count_and_check(node.left, total_nodes)
        right_count, right_found = count_and_check(node.right, total_nodes)

        if left_found or right_found:
            return 0, True  # Early termination if split already found

        current_count = 1 + left_count + right_count
        split_possible = current_count == total_nodes // 2

        return current_count, split_possible

    if not root:
        return False

    # First pass to count total nodes
    def count_total(node):
        if not node:
            return 0
        return 1 + count_total(node.left) + count_total(node.right)

    total = count_total(root)
    if total % 2 != 0:
        return False

    _, result = count_and_check(root, total)
    return result

# Original approach for comparison (keeping the function names you had)
def count_nodes(root):
    if not root:
        return 0
    return 1 + count_nodes(root.left) + count_nodes(root.right)

def can_split_helper(root, total_nodes):
    if not root:
        return 0

    left_count = can_split_helper(root.left, total_nodes)
    right_count = can_split_helper(root.right, total_nodes)

    # If any subtree has half of the total nodes, we can split the tree
    if left_count == total_nodes // 2 or right_count == total_nodes // 2:
        can_split_helper.found = True

    return 1 + left_count + right_count

def can_split(root):
    total_nodes = count_nodes(root)
    if total_nodes % 2 != 0:
        return False

    can_split_helper.found = False
    can_split_helper(root, total_nodes)
    return can_split_helper.found