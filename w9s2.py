'''
Problem 1: Level Order Traversal Of Binary Tree

Understand:
1.  Can the tree be empty?
2.  What should the output look like?

Plan:
1.  Initialize an empty list to hold the levels
2.  Use a queue to perform a level order traversal
3.  For each level, add the values of the nodes to a list
4.  Add the list to the output list
5.  Return the output list

Implement:
'''
from collections import deque
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

def level_order(root):
    if not root:
        return []
    
    result = []
    queue = deque([root])
    
    while queue:
        current = queue.popleft()
        result.append(current.val)
        if current.left:
            queue.append(current.left)
        if current.right:
            queue.append(current.right)
    return result

# Example Usage:

root = TreeNode(4, TreeNode(2, TreeNode(1), TreeNode(3)), TreeNode(6))
print(level_order(root))  # Output: [4, 2, 6, 1, 3]


'''
Problem 3: Odd-Even Level Sum Difference in Binary Tree

Understand:
1.  What if there is only one level?
2.  What level should the root be?

Plan:
1.  Initialize two variables to hold the sum of the odd and even levels
2.  Use a queue to perform a level order traversal
3.  For each level, add the values of the nodes to the appropriate sum
4.  Return the difference between the two sums

Implement:
'''

def level_difference(root):
    if not root:
        return 0
    
    odd_sum, even_sum = 0, 0
    queue = deque([root])
    level = 0
    
    while queue:
        level += 1
        for _ in range(len(queue)):
            current = queue.popleft()
            if level % 2 == 0:
                even_sum += current.val
            else:
                odd_sum += current.val
            if current.left:
                queue.append(current.left)
            if current.right:
                queue.append(current.right)
    return odd_sum - even_sum

# Example Usage:   
root = TreeNode(6, TreeNode(3, TreeNode(5)), TreeNode(8, TreeNode(4, TreeNode(1), TreeNode(7)), TreeNode(2, None, TreeNode(3))))
print(level_difference(root))  # Output: -5


'''
Problem 4: Level Order Traversal of Binary Tree with Nested Lists

Understand:
1.  Can the tree be empty?
2.  What if there is only one value in the level, should it be a list or just the value?

Plan:
1.  Initialize an empty list to hold the levels
2.  Use a queue to perform a level order traversal
3.  For each level, add the values of the nodes to a list
4.  Add the list to the output list
5.  Return the output list

Implement:
'''

def level_order(root):
    if not root:
        return []
    
    result = []
    queue = deque([root])
    
    while queue:
        level = []
        for _ in range(len(queue)):
            current = queue.popleft()
            level.append(current.val)
            if current.left:
                queue.append(current.left)
            if current.right:
                queue.append(current.right)
        result.append(level)
    return result


# Example Usage:   
root = TreeNode(3, TreeNode(9), TreeNode(20, TreeNode(15), TreeNode(7)))
print(level_order(root))  # Output: [[3], [9, 20], [15, 7]]