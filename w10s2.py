'''
Problem 6: Add Two Numbers Represented by Linked Lists

Understand:
1.  Do I have to reverse the linked list?
2.  Are the linked lists in reverse order?

Plan:
1.  Initialize a dummy node to hold the head of the list
2.  Initialize a pointer to the dummy node
3.  Initialize a variable to hold the carry
4.  Iterate through the linked lists
5.  Add the values of the nodes
6.  Update the carry
7.  Create a new node with the value of the sum
8.  Move the pointer to the new node
9.  Return the next node of the dummy node

Implement:
'''
class ListNode:
    def __init__(self, val=0, next=None):
        self.val = val
        self.next = next

def add_two_numbers(l1, l2):
    dummy = ListNode()
    current = dummy
    carry = 0

    while l1 or l2 or carry:
        val1 = l1.val if l1 else 0
        val2 = l2.val if l2 else 0
        carry, remainder = divmod(val1 + val2 + carry, 10)
        current.next = ListNode(remainder)
        current = current.next
        l1 = l1.next if l1 else None

